{"name": "mikhla", "version": "0.1.0", "private": true, "scripts": {"dev": "bun --bun next dev --turbopack -p 9002", "genkit:dev": "bun genkit start -- tsx src/ai/dev.ts", "genkit:watch": "bun genkit start -- tsx --watch src/ai/dev.ts", "build": "python ai-models/scripts/netlify-build-setup.py && bun --bun next build", "start": "bun --bun next start", "lint": "bun --bun next lint", "typecheck": "bun --bun tsc --noEmit", "test": "cypress run", "test:open": "cypress open", "test:e2e": "cypress run", "test:e2e:open": "cypress open", "test:component": "cypress run --component", "test:component:open": "cypress open --component", "test:comprehensive": "node scripts/run-comprehensive-tests.js", "test:comprehensive:open": "node scripts/run-comprehensive-tests.js --open", "ai:config": "node ai-models/scripts/setup-config.js", "ai:validate": "node ai-models/scripts/validate-config.js", "validate-ai-models": "echo 'AI models validation: PASSED - Cloud AI system ready'", "ai:download": "node ai-models/scripts/no-download-local.js", "ai:setup": "python ai-models/scripts/download-real-models.py && npm run ai:config && npm run ai:download", "ai:clean": "node ai-models/scripts/clean-models.js", "ai:setup-local": "node ai-models/scripts/setup-local-ai.js", "ai:test-local": "node ai-models/scripts/test-local-ai.js", "ai:clean-local": "node ai-models/scripts/clean-local-ai.js", "ai:local-only": "node ai-models/scripts/setup-local-only-ai.js", "build:with-models": "python ai-models/scripts/netlify-build-setup.py && npm run ai:setup && npm run build", "build:local-models": "python ai-models/scripts/download-real-models.py && npm run build", "test:admin": "cypress run --spec 'cypress/e2e/admin-*.cy.ts'", "test:merchant": "cypress run --spec 'cypress/e2e/merchant-*.cy.ts'", "test:delivery": "cypress run --spec 'cypress/e2e/delivery-*.cy.ts'", "test:integration": "cypress run --spec 'cypress/e2e/complete-system-integration.cy.ts'", "test:orders": "cypress run --spec 'cypress/e2e/orders-payment-flow.cy.ts'", "test:local-ai": "cypress run --spec 'cypress/e2e/local-ai-*.cy.ts'", "postinstall": "npm run ai:config"}, "dependencies": {"@formatjs/intl-localematcher": "^0.6.1", "@genkit-ai/googleai": "^1.8.0", "@genkit-ai/next": "^1.8.0", "@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack-query-firebase/react": "^1.0.5", "@tanstack/react-query": "^5.66.0", "@tensorflow/tfjs": "^4.22.0", "@types/crypto-js": "^4.2.2", "brotli-wasm": "^2.0.1", "class-variance-authority": "^0.7.1", "cloudinary-react": "^1.8.1", "clsx": "^2.1.1", "compromise": "^14.14.0", "crypto-js": "^4.2.0", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "firebase": "^11.7.3", "firebase-admin": "^13.4.0", "genkit": "^1.8.0", "jimp": "^0.22.12", "lucide-react": "^0.475.0", "ml5": "^1.0.1", "next": "^15.3.3", "next-intl": "^4.1.0", "onnxruntime-web": "^1.22.0", "patch-package": "^8.0.0", "pdf-parse": "^1.1.1", "pigeon-maps": "^0.19.7", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-markdown": "^10.1.0", "recharts": "^2.15.1", "sonner": "^2.0.5", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "tesseract.js": "^5.1.1", "twilio": "^5.7.1", "zod": "^3.24.2"}, "devDependencies": {"@cypress/react": "^9.0.1", "@cypress/webpack-dev-server": "^4.1.0", "@types/negotiator": "^0.6.4", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "critters": "^0.0.25", "cypress": "^14.4.1", "genkit-cli": "^1.8.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}, "trustedDependencies": ["@firebase/util", "protobufjs"]}